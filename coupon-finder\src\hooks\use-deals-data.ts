import { useQuery } from '@tanstack/react-query'
import { useState, useEffect } from 'react'
import { api } from '@/api/hono-client'
import type { TopSellingProduct, BestDeal } from '@/types/deals'

// Helper function để tính discount percentage đúng cách
function calculateDiscountPercentage(price: number, originalPrice: number): number {
  // Validate input
  if (!price || !originalPrice || originalPrice <= 0 || price < 0) {
    return Math.floor(Math.random() * 25) + 5 // Fallback: random 5-30%
  }

  // Nếu price >= originalPrice thì không có discount
  if (price >= originalPrice) {
    return 0
  }

  // Tính discount percentage
  const discount = ((originalPrice - price) / originalPrice) * 100

  // Validate kết quả: discount phải trong khoảng 0-100%
  if (discount < 0 || discount > 100) {
    return Math.floor(Math.random() * 25) + 5 // Fallback: random 5-30%
  }

  // Làm tròn về số nguyên
  return Math.round(discount)
}

// Types cho deals data
export interface TopSellingProduct {
  id: string
  name: string
  store: string
  startDate: string
  endDate: string | null
  rating: number
  monthlySales: number
  image: string
  merchant: string
  affiliateLink: string
}

export interface BestDeal {
  id: string
  name: string
  store: string
  price: number
  originalPrice: number
  discount: number
  rating: number
  reviews: number
  image: string
  isLimitedTime: boolean
  expiryTime?: string
  merchant: string
  affiliateLink: string
}



// Hook để lấy top selling products từ campaigns
export function useTopSellingProducts() {
  return useQuery({
    queryKey: ['deals', 'top-selling'],
    queryFn: async (): Promise<TopSellingProduct[]> => {
      try {
        // Sử dụng campaigns API để lấy các chiến dịch active
        const data = await api.campaigns.getAll({
          status: 'active',
          limit: 6,
          offset: 0
        })

        console.log('Top selling campaigns response:', data) // Debug log

        if (!data.success || !data.data) {
          throw new Error('Invalid response format')
        }

        // Check if campaigns array exists
        if (!data.data.campaigns || !Array.isArray(data.data.campaigns)) {
          console.warn('No campaigns array found in top selling response:', data.data)
          return []
        }

        return data.data.campaigns.map((campaign: any, index: number) => ({
          id: campaign.id || `top-${index}`,
          name: campaign.name || 'Chiến dịch không tên',
          store: campaign.merchant || 'Store',
          startDate: campaign.startDate || new Date().toISOString(),
          endDate: campaign.endDate || null,
          rating: 4.5 + Math.random() * 0.5, // Random rating 4.5-5.0
          monthlySales: Math.floor(Math.random() * 10000) + 1000,
          image: campaign.logo || '/api/placeholder/300/300',
          merchant: campaign.merchant || 'Unknown',
          affiliateLink: campaign.url || '#'
        }))
      } catch (error) {
        console.error('Error fetching top selling campaigns:', error)
        throw error
      }
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })
}

// Hook để lấy best deals từ top selling products API với support cho pagination
export function useBestDeals(limit: number = 20, offset: number = 0) {
  return useQuery({
    queryKey: ['deals', 'best-deals', limit, offset],
    queryFn: async (): Promise<BestDeal[]> => {
      try {
        // Sử dụng top selling products API với fallback về products API
        let data = await api.products.getTopSelling(undefined, limit, offset)

        // Nếu top selling không có data, fallback về products API
        if (!data.success || !data.data || !data.data.products || data.data.products.length === 0) {
          data = await api.products.getAll({
            limit,
            offset
          })
        }

        if (!data.success || !data.data) {
          throw new Error('Invalid response format')
        }

        // Check if products array exists
        if (!data.data.products || !Array.isArray(data.data.products)) {
          return []
        }

        return data.data.products.map((product: any, index: number) => {
          const price = Number(product.price) || 0
          let originalPrice = Number(product.originalPrice) || 0
          let discount = Number(product.discount) || 0

          // Ưu tiên sử dụng discount từ AccessTrade API
          if (discount > 0 && discount <= 100) {
            // Có discount thật từ API, tính originalPrice từ discount
            originalPrice = Math.round(price / (1 - discount / 100))
          } else {
            // Không có discount từ API, tạo discount giả
            if (originalPrice === price && price > 0) {
              // Tạo random discount từ 10-30%
              const randomDiscount = Math.floor(Math.random() * 21) + 10 // 10-30%
              originalPrice = Math.round(price / (1 - randomDiscount / 100))
              discount = randomDiscount
            } else if (!originalPrice && price > 0) {
              // Fallback nếu không có originalPrice
              const randomDiscount = Math.floor(Math.random() * 21) + 10 // 10-30%
              originalPrice = Math.round(price / (1 - randomDiscount / 100))
              discount = randomDiscount
            } else {
              // Tính discount từ price và originalPrice
              discount = calculateDiscountPercentage(price, originalPrice)
            }
          }

          return {
            id: product.id || `deal-${index}`,
            name: product.name || product.description || 'Sản phẩm không tên',
            store: product.merchant || 'Store',
            price,
            originalPrice,
            discount,
            rating: Number(product.rating) || 4.5,
            reviews: Number(product.reviewCount) || Math.floor(Math.random() * 1000) + 100,
            image: product.imageUrl || product.image || '/api/placeholder/300/300',
            isLimitedTime: Math.random() > 0.7, // 30% chance
            expiryTime: Math.random() > 0.5 ? `${Math.floor(Math.random() * 24)} giờ` : `${Math.floor(Math.random() * 7)} ngày`,
            merchant: product.merchant || 'Unknown',
            affiliateLink: product.affiliateLink || product.aff_link || '#'
          }
        })
      } catch (error) {
        console.error('Error fetching best deals (top selling):', error)
        throw error
      }
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })
}

// Hook tổng hợp để lấy tất cả deals data
export function useDealsData(limit?: number, offset?: number) {
  const topSelling = useTopSellingProducts()
  const bestDeals = useBestDeals(limit, offset)

  return {
    topSelling,
    bestDeals,
    isLoading: topSelling.isLoading || bestDeals.isLoading,
    isError: topSelling.isError || bestDeals.isError,
    error: topSelling.error || bestDeals.error
  }
}

// Hook để handle infinite loading cho deals page
export function useInfiniteDeals() {
  const [allBestDeals, setAllBestDeals] = useState<BestDeal[]>([])
  const [currentOffset, setCurrentOffset] = useState(0)
  const [hasMore, setHasMore] = useState(true)
  const [isLoadingMore, setIsLoadingMore] = useState(false)

  const topSelling = useTopSellingProducts()
  const initialDeals = useBestDeals(20, 0) // Load first 20 items

  // Initialize with first batch
  useEffect(() => {
    if (initialDeals.data && allBestDeals.length === 0) {
      setAllBestDeals(initialDeals.data)
      setCurrentOffset(20)
      // If we get less than 20 items, there's no more data
      if (initialDeals.data.length < 20) {
        setHasMore(false)
      }
    }
  }, [initialDeals.data, allBestDeals.length])

  const loadMore = async () => {
    if (isLoadingMore || !hasMore) return

    console.log('🔄 Loading more deals with offset:', currentOffset)
    setIsLoadingMore(true)
    try {
      // Load next batch
      const data = await api.products.getTopSelling(undefined, 20, currentOffset)
      console.log('📦 API response:', {
        success: data.success,
        dataLength: data.data?.products?.length || 0,
        currentOffset,
        allDealsLength: allBestDeals.length
      })

      if (!data.success || !data.data || !data.data.products) {
        console.log('❌ No more data available')
        setHasMore(false)
        return
      }

      const newDeals = data.data.products.map((product: any, index: number) => {
        // Same mapping logic as in useBestDeals
        let price = 0
        let originalPrice = 0
        let discount = 0

        if (product.price) {
          if (typeof product.price === 'string') {
            price = parseFloat(product.price.replace(/[^\d.]/g, '')) || 0
          } else {
            price = Number(product.price) || 0
          }

          originalPrice = price * (1 + Math.random() * 0.5 + 0.1) // 10-60% higher
          discount = calculateDiscountPercentage(price, originalPrice)
        }

        return {
          id: product.id || `deal-${currentOffset + index}`,
          name: product.name || product.description || 'Sản phẩm không tên',
          store: product.merchant || 'Store',
          price,
          originalPrice,
          discount,
          rating: Number(product.rating) || 4.5,
          reviews: Number(product.reviewCount) || Math.floor(Math.random() * 1000) + 100,
          image: product.imageUrl || product.image || '/api/placeholder/300/300',
          isLimitedTime: Math.random() > 0.7,
          expiryTime: Math.random() > 0.5 ? `${Math.floor(Math.random() * 24)} giờ` : `${Math.floor(Math.random() * 7)} ngày`,
          merchant: product.merchant || 'Unknown',
          affiliateLink: product.affiliateLink || product.aff_link || '#'
        }
      })

      setAllBestDeals(prev => {
        const updated = [...prev, ...newDeals]
        console.log('✅ Updated deals:', {
          previousLength: prev.length,
          newDealsLength: newDeals.length,
          totalLength: updated.length
        })
        return updated
      })
      setCurrentOffset(prev => prev + 20)

      // If we get less than 20 items, there's no more data
      if (newDeals.length < 20) {
        console.log('🔚 Reached end of data (got less than 20 items)')
        setHasMore(false)
      }

      // Stop at 200 items (API limit)
      if (currentOffset >= 180) {
        console.log('🔚 Reached API limit (200 items)')
        setHasMore(false)
      }

    } catch (error) {
      console.error('Error loading more deals:', error)
      setHasMore(false)
    } finally {
      setIsLoadingMore(false)
    }
  }

  return {
    topSelling,
    bestDeals: { data: allBestDeals, isLoading: initialDeals.isLoading, isError: initialDeals.isError, error: initialDeals.error },
    loadMore,
    hasMore,
    isLoadingMore,
    isLoading: initialDeals.isLoading,
    isError: initialDeals.isError,
    error: initialDeals.error
  }
}
